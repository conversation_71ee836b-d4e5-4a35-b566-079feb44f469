import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { fsaHraHsaConfig } from '../Config/fsaHraHsaConfig';

export const getClaimsCoverFields = (
  orgDetails: Partial<OrganizationDetails>
): Partial<TemplateFieldConfig>[] => {
  const planTransition = orgDetails?.plan?.plan_transition;

  return [
    {
      label: 'Cover Retail Claims',
      name: fsaHraHsaConfig.fsa_ind,
      value: planTransition?.fsa_ind,
    },
    {
      label: 'Cover Mail Claims',
      name: fsaHraHsaConfig.fsa_substantiation_needed,
      value: planTransition?.fsa_substantiation_needed,
    },
    {
      label: 'Cover Paper Claims',
      name: fsaHraHsaConfig.hra_ind,
      value: planTransition?.hra_ind,
    },
    {
      label: 'Cover Out of Network',
      name: fsaHraHsaConfig.hra_substantiation_needed,
      value: planTransition?.hra_substantiation_needed,
    },
    {
      label: 'Cover Foreign Claims',
      name: fsaHraHsaConfig.hsa_ind,
      value: planTransition?.hsa_ind,
    },
    {
      label: 'Allow for Coordination of Benefits (COBS)',
      name: fsaHraHsaConfig.hsa_substantiation_needed,
      value: planTransition?.hsa_substantiation_needed,
    },
    {
      label: 'Medicaid Subrogation Claims',
      name: fsaHraHsaConfig.hsa_substantiation_needed,
      value: planTransition?.hsa_substantiation_needed,
    },
    {
      label: 'Paper Claims Covered - Pricing',
      name: fsaHraHsaConfig.hsa_substantiation_needed,
      value: planTransition?.hsa_substantiation_needed,
    },
  ];
};
