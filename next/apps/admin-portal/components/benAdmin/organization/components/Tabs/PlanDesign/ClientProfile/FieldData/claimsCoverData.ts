import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

import { fsaHraHsaConfig } from '../Config/fsaHraHsaConfig';

export const getClaimsCoverFields = (
  orgDetails: Partial<OrganizationDetails>
): Partial<TemplateFieldConfig>[] => {
  const planPdx = orgDetails?.plan?.plan_pdx;

  return [
    {
      label: 'Cover Retail Claims',
      name: fsaHraHsaConfig.fsa_ind,
      value: planPdx?.fsa_ind,
    },
    {
      label: 'Cover Mail Claims',
      name: fsaHraHsaConfig.fsa_substantiation_needed,
      value: planPdx?.fsa_substantiation_needed,
    },
    {
      label: 'Cover Paper Claims',
      name: fsaHraHsaConfig.hra_ind,
      value: planPdx?.hra_ind,
    },
    {
      label: 'Cover Out of Network',
      name: fsaHraHsaConfig.hra_substantiation_needed,
      value: planPdx?.hra_substantiation_needed,
    },
    {
      label: 'Cover Foreign Claims',
      name: fsaHraHsaConfig.hsa_ind,
      value: planPdx?.hsa_ind,
    },
    {
      label: 'Allow for Coordination of Benefits (COBS)',
      name: fsaHraHsaConfig.hsa_substantiation_needed,
      value: planPdx?.hsa_substantiation_needed,
    },
    {
      label: 'Medicaid Subrogation Claims',
      name: fsaHraHsaConfig.hsa_substantiation_needed,
      value: planPdx?.hsa_substantiation_needed,
    },
    {
      label: 'Paper Claims Covered - Pricing',
      name: fsaHraHsaConfig.hsa_substantiation_needed,
      value: planPdx?.hsa_substantiation_needed,
    },
  ];
};
