// claimsCoverConfig.ts

import { getPropertyPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { BASE_PATHS } from './configs';

// Define field names without full paths
const FSA_HRA_HSA_FIELDS = {
  fsa_ind: 'fsa_ind',
  fsa_substantiation_needed: 'fsa_substantiation_needed',
  hra_ind: 'hra_ind',
  hra_substantiation_needed: 'hra_substantiation_needed',
  hsa_ind: 'hsa_ind',
  hsa_substantiation_needed: 'hsa_substantiation_needed',
};

/**
 * Type definition for the flattened FSA/HRA/HSA configuration
 */
export interface FsaHraHsaConfig {
  fsa_ind: string;
  fsa_substantiation_needed: string;
  hra_ind: string;
  hra_substantiation_needed: string;
  hsa_ind: string;
  hsa_substantiation_needed: string;
}

// Generate the full paths with explicit property assignments
export const claimsCoverConfig: FsaHraHsaConfig = {
  fsa_ind: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    FSA_HRA_HSA_FIELDS.fsa_ind
  ),
  fsa_substantiation_needed: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    FSA_HRA_HSA_FIELDS.fsa_substantiation_needed
  ),
  hra_ind: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    FSA_HRA_HSA_FIELDS.hra_ind
  ),
  hra_substantiation_needed: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    FSA_HRA_HSA_FIELDS.hra_substantiation_needed
  ),
  hsa_ind: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    FSA_HRA_HSA_FIELDS.hsa_ind
  ),
  hsa_substantiation_needed: getPropertyPath(
    BASE_PATHS.PLAN_TRANSITION,
    FSA_HRA_HSA_FIELDS.hsa_substantiation_needed
  ),
};

/**
 * getClaimsCoverConfigPath
 * Returns a single path for the given field.
 */
export function getClaimsCoverConfigPath(field: keyof FsaHraHsaConfig): string {
  return claimsCoverConfig[field];
}
